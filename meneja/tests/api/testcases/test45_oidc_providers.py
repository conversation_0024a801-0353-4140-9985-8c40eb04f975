# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

# pylint: disable=no-member

"""
Tests for OIDC provider endpoints
"""


from pytest import mark

from tests.api.testcases import VCOApiBaseTestCase


class OIDCProvidersTests(VCOApiBaseTestCase):
    """Test OIDC provider endpoints"""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._set_up_customer()

    @mark.nightly
    def test01_list_oidc_providers(self, mock_list_oidc_providers):
        """Test listing OIDC providers"""
        # Mock the response from the business logic
        mock_list_oidc_providers.return_value = [
            {
                "id": "123",
                "name": "Test Provider",
                "issuer": "https://test.com",
                "clientId": "client123",
                "clientSecret": "secret123",
                "tokenEndpoint": "https://test.com/token",
                "userinfoEndpoint": "https://test.com/userinfo",
                "jwksUri": "https://test.com/jwks",
                "scope": "openid profile email",
                "active": True,
                "createdAt": **********,
                "updatedAt": **********,
            }
        ]

        # Make the request
        response = self.vco_api.admin.listOIDCProviders()

        # Check the response
        self.assertEqual(len(response["results"]), 1)
        self.assertEqual(response["results"][0]["name"], "Test Provider")
        mock_list_oidc_providers.assert_called_once()

    @mark.nightly
    def test02_create_oidc_provider(self, mock_create_oidc_provider):
        """Test creating an OIDC provider"""
        # Mock the response from the business logic
        mock_create_oidc_provider.return_value = {
            "id": "123",
            "name": "Test Provider",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "active": True,
            "createdAt": **********,
            "updatedAt": **********,
        }

        # Create the request data
        data = {
            "name": "Test Provider",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "claimKey": "organization",
            "claimValue": "cairo-cloud",
        }

        # Make the request
        response = self.vco_api.admin.createOIDCProvider(payload=data)

        # Check the response
        self.assertEqual(response["name"], "Test Provider")
        mock_create_oidc_provider.assert_called_once_with(self.vco_id, data)

    @mark.nightly
    def test03_get_oidc_provider(self, mock_get_oidc_provider):
        """Test getting an OIDC provider by ID"""
        # Mock the response from the business logic
        mock_get_oidc_provider.return_value = {
            "id": "123",
            "name": "Test Provider",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "active": True,
            "createdAt": **********,
            "updatedAt": **********,
        }

        # Make the request
        response = self.vco_api.admin.getOIDCProvider(provider_id="123")

        # Check the response
        self.assertEqual(response["name"], "Test Provider")
        mock_get_oidc_provider.assert_called_once_with(self.vco_id, "123")

    @mark.nightly
    def test04_update_oidc_provider(self, mock_update_oidc_provider):
        """Test updating an OIDC provider"""
        # Mock the response from the business logic
        mock_update_oidc_provider.return_value = {
            "id": "123",
            "name": "Updated Provider",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "active": True,
            "createdAt": **********,
            "updatedAt": **********,
        }

        # Create the request data
        data = {
            "name": "Updated Provider",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
        }

        # Make the request
        response = self.vco_api.admin.updateOIDCProvider(provider_id="123", payload=data)

        # Check the response
        self.assertEqual(response["name"], "Updated Provider")
        mock_update_oidc_provider.assert_called_once_with(self.vco_id, "123", data)

    @mark.nightly
    def test05_delete_oidc_provider(self, mock_delete_oidc_provider):
        """Test deleting an OIDC provider"""
        # Mock the response from the business logic
        mock_delete_oidc_provider.return_value = True

        # Make the request
        self.vco_api.admin.deleteOIDCProvider(provider_id="123")

        # Check the response
        mock_delete_oidc_provider.assert_called_once_with(self.vco_id, "123")

    @mark.nightly
    def test06_activate_oidc_provider(self, mock_activate_oidc_provider):
        """Test activating an OIDC provider"""
        # Mock the response from the business logic
        mock_activate_oidc_provider.return_value = True

        # Make the request
        response = self.vco_api.admin.activateOIDCProvider(provider_id="123")

        # Check the response
        self.assertEqual(response["success"], True)
        mock_activate_oidc_provider.assert_called_once_with(self.vco_id, "123")

    @mark.nightly
    def test07_deactivate_oidc_provider(self, mock_deactivate_oidc_provider):
        """Test deactivating an OIDC provider"""
        # Mock the response from the business logic
        mock_deactivate_oidc_provider.return_value = True

        # Make the request
        response = self.vco_api.admin.deactivateOIDCProvider(provider_id="123")

        # Check the response
        self.assertEqual(response["success"], True)
        mock_deactivate_oidc_provider.assert_called_once_with(self.vco_id, "123")

    @mark.nightly
    def test08_create_oidc_provider_with_claims(self, mock_create_oidc_provider):
        """Test creating an OIDC provider with claim validation"""
        # Mock the response from the business logic
        mock_create_oidc_provider.return_value = {
            "id": "123",
            "name": "Test Provider with Claims",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "claimKey": "organization",
            "claimValue": "cairo-cloud",
            "active": True,
            "createdAt": **********,
            "updatedAt": **********,
        }

        # Create the request data with claim validation
        data = {
            "name": "Test Provider with Claims",
            "issuer": "https://test.com",
            "clientId": "client123",
            "clientSecret": "secret123",
            "tokenEndpoint": "https://test.com/token",
            "userinfoEndpoint": "https://test.com/userinfo",
            "jwksUri": "https://test.com/jwks",
            "scope": "openid profile email",
            "claimKey": "organization",
            "claimValue": "cairo-cloud",
        }

        # Make the request
        response = self.vco_api.admin.createOIDCProvider(payload=data)

        # Check the response includes claim fields
        self.assertEqual(response["name"], "Test Provider with Claims")
        self.assertEqual(response["claimKey"], "organization")
        self.assertEqual(response["claimValue"], "cairo-cloud")
        mock_create_oidc_provider.assert_called_once_with(self.vco_id, data)
