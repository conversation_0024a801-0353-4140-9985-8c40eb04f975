# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON>UC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

"""
Data structures for OIDC providers
"""

from dataclasses import dataclass, field

from meneja.structs.dataclasses import BaseStruct


@dataclass
class OIDCProviderStruct(BaseStruct):
    """Struct to holding information of OIDC Provider"""

    id: str = field(metadata=dict(help_text="Unique identifier for the OIDC provider"))
    name: str = field(metadata=dict(help_text="Display name for the OIDC provider"))
    issuer: str = field(metadata=dict(help_text="The issuer URL for the OIDC provider"))
    clientId: str = field(metadata=dict(help_text="Client ID for authenticating with the OIDC provider"))
    clientSecret: str = field(metadata=dict(help_text="Client secret for authenticating with the OIDC provider"))
    scope: str = field(metadata=dict(help_text="Scope to request from the OIDC provider"))
    claimKey: str = field(metadata=dict(help_text="Optional claim name to validate for access control"))
    claimValue: str = field(metadata=dict(help_text="Required value for the claim key"))
    active: bool = field(metadata=dict(help_text="Whether the OIDC provider is active"))
    createdAt: int = field(metadata=dict(help_text="Timestamp when the provider was created"))
    updatedAt: int = field(metadata=dict(help_text="Timestamp when the provider was last updated"))


@dataclass
class OIDCProviderCreateStruct(BaseStruct):
    """Struct to holding information for creating a new OIDC provider"""

    name: str = field(metadata=dict(help_text="Display name for the OIDC provider"))
    issuer: str = field(metadata=dict(help_text="The issuer URL for the OIDC provider"))
    clientId: str = field(metadata=dict(help_text="Client ID for authenticating with the OIDC provider"))
    clientSecret: str = field(metadata=dict(help_text="Client secret for authenticating with the OIDC provider"))
    scope: str = field(default="openid profile email", metadata=dict(help_text="Scope to request from the OIDC provider"))
    claimKey: str = field(default="", metadata=dict(help_text="Optional claim name to validate for access control"))
    claimValue: str = field(default="", metadata=dict(help_text="Required value for the claim key"))