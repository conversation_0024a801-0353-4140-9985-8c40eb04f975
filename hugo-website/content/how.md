---
title: "whitesky platform Stack Service Model"
description: "How the layered architecture of the whitesky offering builds on top of the core, enabling flexibility for different customer profiles and operational needs."
layout: "single_with_left_title"
weight: 20
draft: false
menu:
  main:
    name: "Stack Model"
    url: "/how/"
    weight: 20

keywords:
  - whitesky platform
  - cloud stack architecture
  - cloud as a service
  - sovereign cloud layers
  - paas caas iaas
  - managed cloud
  - self-managed cloud
  - freemium cloud infrastructure

params:
  opengraph:
    title: "whitesky platform Stack Service Model"
    description: "Explore the layered service model of whitesky.cloud — from freemium deployments to fully managed clouds and value-added partner services."
    image: "/images/products-services.png"
    type: "website"

  structured:
    contactType: "Sales"
    email: "<EMAIL>"
    availableLanguage:
      - en
---


This page describes the layered architecture of the whitesky offering. Each outer layer builds on top of the core, enabling flexibility for different customer profiles and operational needs.

![Products & Services](/images/products-services.png)

## Layer 0: Core – Fully In-House Developed Cloud Stack

**Description:** Plain vanilla cloud stack, 100% IP-owned by whitesky.cloud.

- **Cloudspaces**: IaaS with flexible virtual machines and integrated firewalls  
- **Containerspaces**: Kubernetes-as-a-Service (via Rancher)  
- **Objectspaces**: S3-compatible object storage  
- **Integrated metered billing system** (FinOps ready)

## Layer 1: Freemium Cloud Stack (*)

**Description:** Free access to the cloud stack software for individual use.

- Downloadable source code and installation guides  
- No direct support from whitesky.cloud (community support only)  
- Limited to a single-node (mononode) installation  
- **Target Audience**: Hobbyists, developers, research labs

## Layer 2: Self-Managed Cloud (*)

**Description:** Customers operate the stack independently with support escalation to whitesky.cloud.

- 4th-line deep technical support by whitesky.cloud  
- Customer manages all day-to-day operations and hardware  
- Access to software updates and documentation  
- **Target Audience**: Experienced IT teams and DevOps units

## Layer 3: Cloud as a Service (CaaS)

**Description:** whitesky.cloud manages the full cloud environment on customer premises.

- Proactive 24/7 monitoring and system updates  
- Operational relief – no in-house cloud expertise needed  
- 3rd-line support and automated health checks included  
- **Target Audience**: MSPs, governments, corporates without cloud teams

## Layer 4: Platform as a Service (PaaS) – by Partners

**Description:** Value-added services on top of the managed cloud infrastructure.

- whitesky.cloud partners deliver ready-to-use services (e.g., managed databases, CI/CD, WAF)  
- Service-level agreements (SLAs) per service  
- APIs and tools available for integration and automation  
- **Target Audience**: SaaS providers, R&D teams, application developers

---

(*) Not yet available