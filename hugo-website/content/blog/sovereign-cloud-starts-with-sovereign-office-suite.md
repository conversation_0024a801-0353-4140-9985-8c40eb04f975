---
title: "Sovereign Cloud Starts with a Sovereign Office Suite"
subtitle: "Taking back control of your data by reclaiming ownership of your digital office environment"
date: 2025-03-03
author: "<PERSON><PERSON>"
tags: ["sovereign-cloud", "data-sovereignty", "nextcloud", "mail-server", "office-suite"]
featured_image: "/images/blogs/office.jpg"
summary: "When we think of a sovereign cloud, we often associate it with digital independence—the ability to control who can and cannot access our digital assets. The best place to start is with your digital office environment."
include_footer: true
---

When we think of a sovereign cloud, we often associate it with digital independence—the ability to control who can and cannot access our digital assets.

Over the years, most of us have embraced Google Workspace or Microsoft 365. They're easy to use, always available, and accessible from anywhere. It's a seamless experience. But in exchange for that convenience, we've given up control over our data.

We trust these providers to handle our information responsibly, and until recently, that trust may have been well-placed. But times are changing—fast.

If you truly want to take back control of our data, the best place to start is with your digital office environment. By reclaiming ownership of your office suite, you can show the entire organization why full data sovereignty matters—and take the first step toward a truly sovereign cloud.

## How Difficult Is It?

To understand what's available today for building a sovereign office setup, I spent the last few days setting up a fully functional demonstration environment.

Here's what I needed:

- **Email** – Accessible via web interface, phone, and desktop client
- **File sharing**
- **Video conferencing & chat**
- **Productivity tools:**
  - Word processor
  - Spreadsheets
  - Presentation builder

The biggest challenge? Setting up a proper email server. Email requires configuring multiple protocols like DKIM and DMARC, which can be intimidating to get right.

For the rest, I wanted an all-in-one system where everything integrates seamlessly.

For email, I chose **Mail-in-a-Box** from a longer list of options. I picked it because it also acts as a DNS server and claims to handle the tricky configurations automatically by creating the necessary DNS records itself.

For everything else, I went with **Nextcloud**. It's been getting a lot of attention lately, and I wanted to see for myself what it can do.

## The Architecture I Chose for This Experiment

The setup consists of:

- **Cloudspace** – A **whitesky cloudspace** is a private Layer 2 network, isolated from external networks by a built-in virtual firewall.
- **Two Virtual Machines (VMs)** – One for the mail server and one for Nextcloud.
- **Objectspace** – A **whitesky objectspace** is an S3-compatible object storage provider that connects to the private network of a cloudspace.

### How It Works

- The **mail server** acts as the **Mail Transfer Agent (MTA)** and serves as the central authentication source for Nextcloud.
- It supports standard mail protocols like **SMTP, POP3, and IMAP** and includes a web portal for administration and email access.
- For webmail, the easiest option is to use the built-in mail client in **Nextcloud's web interface**.

The **Nextcloud server** stores all files in an **Objectspace bucket**, making the setup highly scalable from the start.

Both the Nextcloud and mail servers are managed via **SSH over a WireGuard VPN tunnel** that was added to the Cloudspace.

## Setting Up the Mail Server

The first step in setting up a mail server is configuring DNS. Normally, this can be complex, but **Mail-in-a-Box** makes it easy by acting as its own DNS server, handling most of the setup automatically.

The installation process includes built-in **pre-flight checks**, ensuring things like **PTR records** are correctly set up. It then walks you through the entire setup, step by step.

It took me about two hours—I made a mistake and had to restart—but the effort was well worth it.

Now, I have my **own sovereign, independent mail server**, and it works flawlessly!

Want to test it? Send me an email at **<EMAIL>**—I'll receive it, and you'll get a reply. No emails land in spam, and all security certificates are automatically handled by Let's Encrypt.

Managing users and aliases is straightforward, and the built-in webmail works great. For my desktop and phone, I use Thunderbird, which configured my account automatically.

## Setting Up Nextcloud

After evaluating different options, I chose to use the **Docker-based Nextcloud All-In-One** installation method.

This approach starts by deploying the Nextcloud All-In-One Docker container, which provides a user-friendly interface where you can select the components you want in your Nextcloud setup.

Once everything is configured, just hit **Install**, and within **15 minutes**, all the necessary components are **downloaded, running, and fully initialized**—ready to use.

## Integrating Authentication and Storage

To streamline user management, I installed the **External Authentication App** in Nextcloud. This allows **me to add users only on the mail server**, and Nextcloud will authenticate them against it.

A more robust approach would be to use **LDAP**, so both the mail server and Nextcloud authenticate against a single directory service. However, **Mail-in-a-Box doesn't support LDAP**. Other mail solutions do, but they can be trickier to configure from a DNS perspective.

To set up external authentication, I had to log into the Nextcloud server and access the Nextcloud Docker container to configure authentication manually. Even so, it was up and running in just 5 minutes.

### Configuring Scalable Storage with Objectspaces

Next, I **deployed an Objectspace** within the same Cloudspace and configured Nextcloud to use Objectspace as its **primary storage**.

Why?

- Like this, Nextcloud only stores metadata, while files are stored in an S3-compatible object storage (Objectspace).
- This makes the Nextcloud server **highly scalable**, as it doesn't handle file storage directly.

To make this work, I had to access the Nextcloud Docker container again to modify the configuration manually.

## Day 2 Operations: Backup & Upgrades

**Backup**
Both **Mail-in-a-Box** and **Nextcloud** come with **built-in backup mechanisms**, making it easy to protect and restore data if needed.

**Upgrades**
Keeping the system up to date is just as simple:

- **Mail-in-a-Box** has a streamlined update process that ensures all components stay secure and functional.
- **Nextcloud** also offers an **easy upgrade path**, allowing you to apply updates without hassle.

With both solutions, **maintenance is minimal**, ensuring **a secure and up-to-date sovereign office suite** with minimal effort.

## It's Not Difficult!

In just a few hours, I had my sovereign office suite up and running—and I was blown away by the quality of both Mail-in-a-Box and Nextcloud.

Every application I needed was available and worked flawlessly. In fact, I'm writing this very blog post in the web-based word processor that comes standard with Nextcloud. And it feels just as smooth as the Office 365 editor I'm used to.

But there's **one big difference—I know exactly where my data is stored!**
