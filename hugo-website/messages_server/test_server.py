#!/usr/bin/env python3
"""
Test script for the contact form backend
"""

import requests
import json

# Test configuration
BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test the health check endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"Health Check - Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check failed: {e}")
        return False

def test_contact_form():
    """Test the contact form submission"""
    data = {
        'name': 'Test User',
        'email': '<EMAIL>',
        'subject': 'Test Contact Form',
        'message': 'This is a test message from the contact form.'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/submit-form", data=data)
        print(f"Contact Form - Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Contact form test failed: {e}")
        return False

def test_pricing_form():
    """Test the pricing form submission"""
    data = {
        'name': 'Test Company',
        'email': '<EMAIL>',
        'subject': 'Quote Request',
        'message': '''QUOTE REQUEST DETAILS:

Contact Information:
- Phone: ******-0123
- Company: Test Company Inc.

Technical Requirements:
- vCPUs: 16
- Memory: 64 GB
- Block Storage: 2 TB
- Object Storage: 10 TB
- Service Model: self-managed
- Hardware Quote Requested: Yes

Additional Information:
Need GPU support (NVIDIA A100) for AI workloads.'''
    }
    
    try:
        response = requests.post(f"{BASE_URL}/submit-form", data=data)
        print(f"Pricing Form - Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Pricing form test failed: {e}")
        return False

def test_rate_limiting():
    """Test rate limiting by sending multiple requests"""
    print("Testing rate limiting (sending 7 requests rapidly)...")
    
    data = {
        'name': 'Rate Test',
        'email': '<EMAIL>',
        'subject': 'Rate Limit Test',
        'message': 'Testing rate limiting functionality.'
    }
    
    success_count = 0
    for i in range(7):
        try:
            response = requests.post(f"{BASE_URL}/submit-form", data=data)
            result = response.json()
            print(f"Request {i+1} - Status: {response.status_code}, Success: {result.get('success')}")
            if response.status_code == 200 and result.get('success'):
                success_count += 1
        except Exception as e:
            print(f"Request {i+1} failed: {e}")
    
    print(f"Rate limiting test: {success_count}/7 requests succeeded")
    return True

if __name__ == "__main__":
    print("Testing Contact Form Backend Server")
    print("=" * 40)
    
    # Test health check
    print("\n1. Testing Health Check:")
    test_health_check()
    
    # Test contact form
    print("\n2. Testing Contact Form:")
    test_contact_form()
    
    # Test pricing form
    print("\n3. Testing Pricing Form:")
    test_pricing_form()
    
    # Test rate limiting
    print("\n4. Testing Rate Limiting:")
    test_rate_limiting()
    
    print("\nTesting completed!")
