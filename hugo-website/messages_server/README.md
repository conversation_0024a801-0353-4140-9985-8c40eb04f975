# Contact Form Backend Server with Daily Email Reporting

A Flask-based backend server to handle contact form submissions with comprehensive daily email reporting.

## Features

- **Email Forwarding**: Forwards form submissions via SMTP
- **Rate Limiting**: IP-based rate limiting using token bucket algorithm (5 tokens max, 1 token per minute refill)
- **CORS Support**: Configured for Hugo frontend integration
- **Security**: Input validation and sanitization
- **Bot Detection**: Advanced client-side and server-side bot detection
- **Logging**: Comprehensive logging for monitoring and debugging
- **Health Check**: `/health` endpoint for monitoring
- **Daily Email Reports**: Automated daily summary emails with all form submissions
- **Multi-Form Support**: Handles contact, pricing, and section5 forms
- **Form Logging**: Detailed logging of all form submissions with essential data

## New Daily Email Reporting Features

### Form Submission Logging
- All form submissions are logged with `logger.info()` statements
- Captures essential data: name, email, subject, message content, form type, timestamp, IP address
- Maintains existing bot detection and rate limiting functionality

### In-Memory Data Storage
- Thread-safe in-memory storage for daily form submission collection
- Stores submissions from all three form types: contact, pricing, section5
- Data persists until daily email is sent

### Daily Email Summary
- Automated daily email sent at 7:00 PM
- Includes all form submissions from the past 24 hours
- Clean, modern HTML email template
- Compatible with all major email providers (Gmail, Outlook, Apple Mail, etc.)
- Organizes submissions by form type for easy reading

### HTML Email Template
- Responsive design that works across email clients
- Avoids complex CSS that might break in email clients
- Includes submission timestamp, sender details, and message content
- Summary section with total counts by form type
- Professional formatting with clear visual hierarchy

## Setup

1. **Install Dependencies**:
   ```bash
   cd messages_server
   pip install -r requirements.txt
   ```

2. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with SMTP settings
   ```

3. **Required Environment Variables**:
   - `SMTP_USERNAME`: SMTP username
   - `SMTP_PASSWORD`: SMTP password (use app password for Gmail)
   - `TO_EMAIL`: Destination email for form submissions

## Running the Server

### Development
```bash
FLASK_ENV=development python app.py
```

### Production
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## API Endpoints

### POST /submit-form
Handles contact form submissions.

**Request Body** (form-data):
- `name` (required): Sender's name
- `email` (required): Sender's email
- `subject` (optional): Email subject
- `message` (required): Message content

**Response**:
```json
{
  "success": true,
  "message": "Thank you! Your message has been sent successfully."
}
```

### GET /health
Health check endpoint (now includes pending submission count).

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00",
  "pending_submissions": 3
}
```

### POST /test-daily-summary
Test endpoint to manually trigger daily summary email.

**Response**:
```json
{
  "success": true,
  "message": "Daily summary email sent successfully"
}
```

## Daily Email Reporting

### Automatic Scheduling
- Daily summary emails are sent automatically at 7:00 PM
- Uses APScheduler with CronTrigger for reliable scheduling
- Graceful shutdown handling to prevent data loss

### Manual Testing
You can manually trigger a daily summary email for testing:
```bash
curl -X POST http://localhost:5000/test-daily-summary
```

### Email Content
The daily summary email includes:
- Total submission count for the day
- Breakdown by form type (contact, pricing, section5)
- Detailed information for each submission:
  - Sender name and email
  - Subject line
  - Full message content
  - Timestamp
  - IP address
- Professional HTML formatting

### Form Type Detection
The server automatically detects form types based on:
- **Pricing forms**: Message content starts with "QUOTE REQUEST DETAILS:"
- **Section5 forms**: HTTP referrer contains "section5"
- **Contact forms**: Default for all other submissions

## Testing

A comprehensive test script is provided to verify functionality:

```bash
cd messages_server
python test_daily_summary.py
```

This script tests:
- Form submissions for all three form types
- Daily summary email generation
- Data storage and retrieval
- Server health monitoring

## Rate Limiting

- Each IP address gets 5 tokens maximum
- Tokens refill at 1 token per minute
- When rate limited, returns success but doesn't send email
- Prevents spam while maintaining user experience

## SMTP Configuration

### Gmail Setup
1. Enable 2-factor authentication
2. Generate an app password
3. Use app password in `SMTP_PASSWORD`

### Other Providers
Update `SMTP_SERVER` and `SMTP_PORT` accordingly:
- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **Custom SMTP**: Your provider's settings

## Deployment

The server is designed to be deployed behind a reverse proxy (nginx, Apache) or on platforms like:
- Heroku
- DigitalOcean App Platform
- AWS Elastic Beanstalk
- Docker containers

## Monitoring

- Logs are written to `contact_form.log` and stdout
- Use the `/health` endpoint for uptime monitoring
- Monitor log files for rate limiting and errors
- Check server health and pending submissions:
  ```bash
  curl http://localhost:5000/health
  ```
- Monitor daily email summary logs for successful delivery
- Form submission logs include detailed information for all submissions

### Log Examples

**Form Submission Logging**:
```
[2024-01-01 14:30:15] INFO in app: Form submission successful: contact form from John Doe (<EMAIL>) - Subject: Contact Form Test
[2024-01-01 14:30:15] INFO in app: Form submission logged: contact form from John Doe (<EMAIL>)
```

**Daily Summary Logging**:
```
[2024-01-01 19:00:00] INFO in app: Sending daily summary with 5 form submissions
[2024-01-01 19:00:01] INFO in app: Daily summary email sent successfully with 5 submissions
```

**Bot Detection Logging**:
```
[2024-01-01 14:25:10] INFO in app: Bot submission blocked from IP *************, reason: too_fast
```
