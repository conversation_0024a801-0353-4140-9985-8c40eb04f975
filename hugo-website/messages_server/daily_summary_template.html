<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Form Submissions Summary</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0; background-color: #f8f9fa;">
    <div style="max-width: 1200px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">

        <!-- Header -->
        <div style="background: linear-gradient(135deg, #0094ca 0%, #00b8e6 100%); color: #ffffff; padding: 30px 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px; font-weight: 600;">
                📊 Daily Form Submissions Summary
            </h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">{{ date }}</p>
        </div>

        <!-- Content -->
        <div style="padding: 30px 20px;">
            <!-- Summary Section -->
            <div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 20px; margin: 20px 0; border-radius: 0 6px 6px 0;">
                <h3 style="margin: 0 0 15px 0; color: #0094ca; font-size: 18px; font-weight: 600;">Summary</h3>
                <div style="margin: 15px 0;">
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Total Submissions:</span>
                        <span style="color: #333333;">{{ total_submissions }}</span>
                    </div>
                    {% if has_submissions %}
                    <div style="margin-bottom: 10px;">
                        <span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Form Types:</span>
                    </div>
                    <ul style="margin: 10px 0 0 20px; padding: 0;">
                        {% for form_type, submissions in grouped_submissions.items() %}
                        <li style="margin-bottom: 5px;">
                            <strong>{{ form_type.title() }} Forms:</strong> {{ submissions|length }} submission{{ 's' if submissions|length != 1 else '' }}
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <div style="margin-bottom: 10px;">
                        <span style="color: #666666; font-style: italic;">No submissions received today</span>
                    </div>
                    {% endif %}
                </div>
            </div>

            {% if has_submissions %}
            <!-- Form Submissions by Type -->
            {% for form_type, submissions in grouped_submissions.items() %}
            <div style="margin: 30px 0;">
                <h2 style="color: #0094ca; font-size: 20px; font-weight: 600; margin: 0 0 20px 0; border-bottom: 2px solid #e9ecef; padding-bottom: 10px;">
                    {{ form_type.title() }} Form Submissions ({{ submissions|length }})
                </h2>

                {% for submission in submissions %}
                <div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; margin: 15px 0; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);">
                    <!-- Submission Header -->
                    <div style="background: linear-gradient(135deg, #0094ca 0%, #00b8e6 100%); color: #ffffff; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 6px 6px 0 0; font-weight: 600;">
                        Submission #{{ loop.index }} - {{ submission.timestamp }}
                    </div>

                    <!-- Contact Information -->
                    <div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 15px; margin: 15px 0; border-radius: 0 4px 4px 0;">
                        <h4 style="margin: 0 0 10px 0; color: #0094ca; font-size: 16px; font-weight: 600;">Contact Information</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Name:</span>
                            <span style="color: #333333;">{{ submission.name }}</span>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Email:</span>
                            <span style="color: #333333;">{{ submission.email }}</span>
                        </div>
                        {% if submission.phone %}
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Phone:</span>
                            <span style="color: #333333;">{{ submission.phone }}</span>
                        </div>
                        {% endif %}
                        {% if submission.company %}
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Company:</span>
                            <span style="color: #333333;">{{ submission.company }}</span>
                        </div>
                        {% endif %}
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">Subject:</span>
                            <span style="color: #333333;">{{ submission.subject or 'No subject' }}</span>
                        </div>
                        <div style="margin-bottom: 8px;">
                            <span style="font-weight: 600; color: #555555; display: inline-block; width: 120px;">IP Address:</span>
                            <span style="color: #333333;">{{ submission.ip_address }}</span>
                        </div>
                    </div>

                    <!-- Technical Requirements (pricing form only) -->
                    {% if form_type == 'pricing' and (submission.vcpus or submission.memory or submission.blockStorage or submission.objectStorage) %}
                    <div style="background: linear-gradient(135deg, #0094ca 0%, #00b8e6 100%); color: #ffffff; padding: 15px; margin: 15px 0; border-radius: 6px;">
                        <h4 style="margin: 0 0 10px 0; color: #ffffff; font-size: 16px; font-weight: 600;">📊 Technical Requirements</h4>
                        {% if submission.vcpus %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #ffffff; display: inline-block; width: 140px;">vCPUs:</span> {{ submission.vcpus }}</div>{% endif %}
                        {% if submission.memory %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #ffffff; display: inline-block; width: 140px;">Memory:</span> {{ submission.memory }} GB</div>{% endif %}
                        {% if submission.blockStorage %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #ffffff; display: inline-block; width: 140px;">Block Storage:</span> {{ submission.blockStorage }} TB</div>{% endif %}
                        {% if submission.objectStorage %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #ffffff; display: inline-block; width: 140px;">Object Storage:</span> {{ submission.objectStorage }} TB</div>{% endif %}
                    </div>
                    {% endif %}

                    <!-- Service Details (pricing form only) -->
                    {% if form_type == 'pricing' and (submission.serviceModel or submission.hardwareQuote) %}
                    <div style="background-color: #f8f9fa; border-left: 4px solid #0094ca; padding: 15px; margin: 15px 0; border-radius: 0 4px 4px 0;">
                        <h4 style="margin: 0 0 10px 0; color: #0094ca; font-size: 16px; font-weight: 600;">Service Configuration</h4>
                        {% if submission.serviceModel %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Service Model:</span> {{ submission.serviceModel }}</div>{% endif %}
                        {% if submission.hardwareQuote %}<div style="margin-bottom: 6px;"><span style="font-weight: 600; color: #555555; display: inline-block; width: 140px;">Hardware Quote:</span> {{ 'Yes' if submission.hardwareQuote == 'on' else 'No' }}</div>{% endif %}
                    </div>
                    {% endif %}

                    <!-- Message -->
                    <div style="background-color: #ffffff; border: 1px solid #e9ecef; border-radius: 6px; padding: 15px; margin: 15px 0;">
                        <h4 style="margin: 0 0 10px 0; color: #333333; font-size: 16px; font-weight: 600;">Message</h4>
                        <div style="background-color: #f8f9fa; padding: 12px; border-radius: 4px; border-left: 3px solid #0094ca; font-size: 14px; line-height: 1.6; white-space: pre-wrap;">{{ submission.message }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Footer -->
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #e9ecef;">
            <div style="font-size: 13px; color: #6c757d; margin-top: 10px;">
                Generated on {{ generated_time }}
            </div>
        </div>
    </div>
</body>
</html>
