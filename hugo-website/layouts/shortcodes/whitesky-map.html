<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<style>
  #whitesky-map { height: 500px; width: 100%; margin-bottom: 2rem; }
</style>

<div id="whitesky-map"></div>

<script>
  const map = L.map('whitesky-map');

  <PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '&copy; OpenStreetMap contributors'
  }).addTo(map);

  const locations = [
    { name: "Gent, Belgium", coords: [51.05, 3.73] },
    { name: "Machelen, Belgium", coords: [50.901, 4.450] },
    { name: "Kampala, Uganda", coords: [0.3476, 32.5825] },
    { name: "Mauritius", coords: [-20.3484, 57.5522] },
    { name: "Warsaw, Poland", coords: [52.2297, 21.0122] },
    { name: "Budapest, Hungary", coords: [47.4979, 19.0402] },
    { name: "Schiphol, Netherlands", coords: [52.3105, 4.7683] }
  ];

  const bounds = [];

  locations.forEach(loc => {
    L.marker(loc.coords).addTo(map).bindPopup(loc.name);
    bounds.push(loc.coords);
  });

  map.fitBounds(bounds, { padding: [10,18] });
</script>
