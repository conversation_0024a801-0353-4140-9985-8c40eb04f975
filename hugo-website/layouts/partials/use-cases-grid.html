<!-- Use Cases Grid Partial -->
{{ if .Pages }}
<div class="use-cases-grid">
  {{ range .Pages.ByWeight }}
  <div class="use-case-item">
    <div class="card use-case-card">
      <div class="card-content use-case-content">
        <div class="media">
          <div class="media-content">
            <p class="title is-4 has-text-centered">
              {{ .Title }}
            </p>
            {{ if .Params.sector }}
            <p class="subtitle is-6 has-text-grey has-text-centered use-case-sector">
              <span class="tag is-primary is-light">{{ .Params.sector }}</span>
            </p>
            {{ end }}
          </div>
        </div>

        <div class="content use-case-description">
          <div class="use-case-details">
            <div class="use-case-info">
              <p><strong>Use Case:</strong> {{ .Params.subtitle }}</p>
              <p><strong>Highlights:</strong> {{ .Params.summary }}</p>
              <p><strong>Results:</strong> {{ .Params.results }}</p>
            </div>
          </div>

          {{ if .Params.blog_link }}
          <div class="use-case-action">
            <a href="{{ .Params.blog_link }}" class="button primary-btn raised">
              <span>Learn More</span>
              <span class="icon">
                <i class="fas fa-external-link-alt"></i>
              </span>
            </a>
          </div>
          {{ end }}
        </div>
      </div>
    </div>
  </div>
  {{ end }}
</div>
{{ end }}
