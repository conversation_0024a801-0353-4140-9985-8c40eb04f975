{{- $section2 := .Site.Params.section2 }}
{{- $title    := index $section2 "title" }}
{{- $subtitle := index $section2 "subtitle" }}
{{- $features := index $section2 "features" }}
<section class="section is-medium" id="section2">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2">{{ $title }}</h2>
      <h3 class="subtitle is-5 is-muted">{{ $subtitle }}</h3>
      <div class="divider is-centered"></div>
    </div>
    {{ $half := div (len $features) 2 }}
    <div class="columns">
      <div class="column is-6">
        {{- range first $half $features }}
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas {{ .icon }} primary-color"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">{{ .title }}</span>

                <span class="icon-box-text">{{ .text | markdownify }}</span>
              </p>
            </div>
          </div>
        </article>
        {{- end }}
      </div>
      <div class="column is-6">
        {{- range after $half $features }}
        <article class="media icon-box">
          <figure class="media-left">
            <p class="image">
              <i class="fas {{ .icon }} primary-color"></i>
            </p>
          </figure>
          <div class="media-content mt-50">
            <div class="content">
              <p>
                <span class="icon-box-title">{{ .title }}</span>

                <span class="icon-box-text">{{ .text | markdownify }}</span>
              </p>
            </div>
          </div>
        </article>
        {{- end }}
      </div>
    </div>
  </div>
</section>