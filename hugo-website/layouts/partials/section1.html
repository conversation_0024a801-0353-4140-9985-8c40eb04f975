{{- $section1 := .Site.Params.section1 }}
{{- $title    := index $section1 "title" }}
{{- $subtitle := index $section1 "subtitle" }}
{{- $tiles    := index $section1 "tiles" }}
<section class="section section-feature-grey is-medium" id="section1">
  <div class="container">
    <div class="title-wrapper has-text-centered">
      <h2 class="title is-2">{{ $title }}</h2>
      <h3 class="subtitle is-5 is-muted">{{ $subtitle }}</h3>
      <div class="divider is-centered"></div>
    </div>

    <div class="content-wrapper">
      <div class="columns">
        {{- range $tiles }}
        <div class="column is-one-third">
          <div class="feature-card is-bordered has-text-centered revealOnScroll delay-1" data-animation="fadeInLeft">
            <div class="card-title">
              <h4>{{ .title }}</h4>
            </div>
            <div class="card-icon">
                <i class="fas {{ .icon }} primary-color" style="font-size:47px; margin-top: 20px; margin-bottom: 20px"></i>
            </div>
            <div class="card-text">
                <p>{{ .text }}</p>
            </div>
            {{/*  <div class="card-action">
              <a href="{{ .url }}" class="button btn-align-md accent-btn raised">{{ .buttonText }}</a>
            </div>  */}}
          </div>
        </div>
        {{- end }}
      </div>
    </div>
  </div>
</section>