/**
 * Bot Detection Library
 * Provides client-side bot detection data collection and server-side validation
 * Used across all forms in the Hugo website
 */

class BotDetection {
  constructor(formId, botDetectionFieldId) {
    this.formId = formId;
    this.botDetectionFieldId = botDetectionFieldId;
    this.form = null;
    this.botDetectionField = null;
    
    // Bot detection variables - collect data for server-side validation
    this.data = {
      pageLoadTime: Date.now(),
      hasMouseMovement: false,
      hasFieldFocus: false,
      minSubmissionTime: 2000, // 2 seconds minimum
      mouseMoveThreshold: 5, // minimum mouse movements
      mouseMoveCount: 0,
      fieldFocusEvents: [],
      keyboardEvents: 0
    };
    
    this.initialized = false;
  }

  /**
   * Initialize bot detection tracking
   * Should be called after DOM is loaded
   */
  init() {
    this.form = document.getElementById(this.formId);
    this.botDetectionField = document.getElementById(this.botDetectionFieldId);
    
    if (!this.form) {
      console.error(`Bot Detection: Form with ID '${this.formId}' not found`);
      return false;
    }
    
    if (!this.botDetectionField) {
      console.error(`Bot Detection: Hidden field with ID '${this.botDetectionFieldId}' not found`);
      return false;
    }
    
    this.setupEventListeners();
    this.initialized = true;
    return true;
  }

  /**
   * Set up event listeners for bot detection
   */
  setupEventListeners() {
    // Track mouse movement
    document.addEventListener("mousemove", () => {
      this.data.mouseMoveCount++;
      if (this.data.mouseMoveCount >= this.data.mouseMoveThreshold) {
        this.data.hasMouseMovement = true;
      }
    });

    // Track field focus and keyboard events
    const formFields = this.form.querySelectorAll('input, textarea, select');
    formFields.forEach(field => {
      // Track field focus
      field.addEventListener("focus", () => {
        this.data.hasFieldFocus = true;
        this.data.fieldFocusEvents.push({
          field: field.name || field.id || 'unknown',
          timestamp: Date.now()
        });
      });

      // Track keyboard events
      field.addEventListener("keydown", () => {
        this.data.keyboardEvents++;
      });
    });
  }

  /**
   * Collect all bot detection data for server-side validation
   * @returns {string} JSON string of bot detection data
   */
  collectData() {
    if (!this.initialized) {
      console.error('Bot Detection: Not initialized. Call init() first.');
      return '{}';
    }

    const submissionTime = Date.now() - this.data.pageLoadTime;
    
    // Check honeypot field
    const honeypot = this.form.querySelector('input[name="_gotcha"]');
    const honeypotFilled = honeypot && honeypot.value.trim() !== '';

    // Collect all bot detection data for server-side validation
    const detectionData = {
      submissionTime: submissionTime,
      hasMouseMovement: this.data.hasMouseMovement,
      mouseMoveCount: this.data.mouseMoveCount,
      hasFieldFocus: this.data.hasFieldFocus,
      fieldFocusEvents: this.data.fieldFocusEvents.length,
      keyboardEvents: this.data.keyboardEvents,
      honeypotFilled: honeypotFilled,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    };

    return JSON.stringify(detectionData);
  }

  /**
   * Add bot detection data to the hidden form field
   * Should be called before form submission
   */
  addDataToForm() {
    if (!this.initialized) {
      console.error('Bot Detection: Not initialized. Call init() first.');
      return false;
    }

    this.botDetectionField.value = this.collectData();
    return true;
  }

  /**
   * Get the form element
   * @returns {HTMLElement|null}
   */
  getForm() {
    return this.form;
  }
}

/**
 * Generic form submission handler with bot detection
 * @param {BotDetection} botDetection - Bot detection instance
 * @param {HTMLElement} submitButton - Submit button element
 * @param {HTMLElement} successMessage - Success message element
 * @param {HTMLElement} errorMessage - Error message element
 * @param {Function} beforeSubmit - Optional callback before submission (for custom data processing)
 */
async function submitFormWithBotDetection(botDetection, submitButton, successMessage, errorMessage, beforeSubmit = null) {
  const form = botDetection.getForm();
  
  // Validate form before submitting
  if (!form.checkValidity()) {
    form.reportValidity();
    return;
  }

  // Hide any previous messages
  successMessage.style.display = "none";
  errorMessage.style.display = "none";

  // Change button text to show loading state
  const originalButtonText = submitButton.textContent;
  submitButton.textContent = "Sending...";
  submitButton.disabled = true;

  try {
    // Execute custom pre-submission logic if provided
    if (beforeSubmit && typeof beforeSubmit === 'function') {
      beforeSubmit();
    }

    // Add bot detection data to hidden field for server-side validation
    botDetection.addDataToForm();

    // Create form data from the form element
    const data = new FormData(form);

    const response = await fetch(form.action, {
      method: form.method,
      body: data,
    });

    const result = await response.json();

    if (response.ok && result.success) {
      successMessage.style.display = "block";
      form.reset();
      // Scroll to success message
      successMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    } else {
      errorMessage.style.display = "block";
      errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  } catch (error) {
    console.error("Form submission error:", error);
    errorMessage.style.display = "block";
    errorMessage.scrollIntoView({ behavior: "smooth", block: "center" });
  }

  submitButton.textContent = originalButtonText;
  submitButton.disabled = false;
}

// Export for use in other scripts
window.BotDetection = BotDetection;
window.submitFormWithBotDetection = submitFormWithBotDetection;
