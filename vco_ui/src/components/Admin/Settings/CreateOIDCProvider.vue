<template>
  <page :loading="loading">
    <template #content>
      <Breadcrumbs :items="breadcrumbs"></Breadcrumbs>
      <v-row>
        <v-col cols="6">
          <div class="font-weight-bold">
            {{ isEdit ? "Edit" : "Create" }} OIDC Provider
          </div>
          <v-divider class="mt-2 mb-5" />

          <v-row>
            <v-col cols="12">
              <help-text-field
                v-model="provider.name"
                label="Name"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                help="A unique name for this OIDC provider"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.issuer"
                label="Issuer URL"
                variant="outlined"
                density="compact"
                :rules="urlRules"
                required
                help="The URL of the OIDC provider's issuer endpoint (will be used to discover endpoints via .well-known)"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.clientId"
                label="Client ID"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                help="The client ID provided by the OIDC provider"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.clientSecret"
                label="Client Secret"
                variant="outlined"
                density="compact"
                :rules="requiredRules"
                required
                help="The client secret provided by the OIDC provider"
                :type="showSecret ? 'text' : 'password'"
                :append-inner-icon="showSecret ? 'mdi-eye-off' : 'mdi-eye'"
                @click:append-inner="showSecret = !showSecret"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="fixedScopes"
                label="Fixed scopes"
                variant="outlined"
                density="compact"
                help="Space-separated list of needed OAuth2 scopes that will be sent with every request"
                placeholder="openid profile email"
                :disabled="true"
              />
            </v-col>
            <v-col cols="12">
              <help-text-field
                v-model="provider.scope"
                label="Optional scopes"
                variant="outlined"
                density="compact"
                required
                help="Space-separated list of additional OAuth2 scopes to request"
                placeholder="optional scopes"
              />
            </v-col>
            <v-col cols="6">
              <help-text-field
                v-model="provider.claimKey"
                label="Claim Key"
                variant="outlined"
                density="compact"
                help="Optional claim name to validate for access control (e.g., 'organization')"
                placeholder="organization"
              />
            </v-col>
            <v-col cols="6">
              <help-text-field
                v-model="provider.claimValue"
                label="Claim Value"
                variant="outlined"
                density="compact"
                help="Required value for the claim key (e.g., 'cairo-cloud')"
                placeholder="cairo-cloud"
              />
            </v-col>
            <v-col cols="12" v-if="isEdit">
              <div class="d-flex align-center">
                <help-text-field
                  v-model="redirectUrl"
                  label="Redirect URL"
                  variant="outlined"
                  density="compact"
                  :disabled="true"
                  help="Use this redirect URL in your OIDC provider configuration"
                  class="flex-grow-1"
                />
                <v-btn
                  size="medium"
                  icon
                  class="mt-n5 ml-2"
                  variant="text"
                  color="primary"
                  @click="copyRedirectUrl"
                  title="Copy to clipboard"
                >
                  <v-icon>mdi-content-copy</v-icon>
                </v-btn>
              </div>
            </v-col>
          </v-row>
          <v-btn
            color="primary"
            type="submit"
            :loading="submitting"
            class="mr-2"
            :disabled="!isValidProvider"
            @click="submit"
          >
            {{ isEdit ? "Update" : "Create" }}
          </v-btn>
          <v-btn variant="text" color="primary" @click="goBack">
            <span class="i18n" id="admin.cancel">{{ $t("admin.cancel") }}</span>
          </v-btn>
        </v-col>
      </v-row>
    </template>
  </page>
</template>

<script>
  import HelpTextField from "../../Base/HelpTextField.vue"

  export default {
    name: "CreateOIDCProvider",
    components: {
      HelpTextField
    },
    data() {
      return {
        submitting: false,
        showSecret: false,
        provider: {
          name: "",
          issuer: "",
          clientId: "",
          clientSecret: "",
          scope: "",
          claimKey: "",
          claimValue: ""
        },
        fixedScopes: "openid profile email",
        requiredRules: [(v) => !!v || "This field is required"],
        urlPattern:
          /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
        loading: true
      }
    },
    computed: {
      isEdit() {
        return !!this.$route.params.id
      },
      breadcrumbs() {
        return [
          { text: this.$t("admin.admin"), disabled: true },
          {
            text: "OIDC Providers",
            exact: true,
            to: { name: "OIDCProviders" }
          },
          { text: this.isEdit ? "Edit" : "Add", disabled: true }
        ]
      },
      urlRules() {
        return [
          (v) => !!v || this.$t("admin.valid-url"),
          (v) => this.urlPattern.test(v) || this.$t("admin.valid-url")
        ]
      },
      isValidProvider() {
        // Check if all required fields are filled and valid
        const requiredFields = [
          this.provider.name,
          this.provider.issuer,
          this.provider.clientId,
          this.provider.clientSecret
        ]

        // Check if all required fields have values
        const allRequiredFieldsFilled = requiredFields.every((field) => !!field)

        // Check if issuer URL is valid
        const isIssuerUrlValid = this.urlPattern.test(this.provider.issuer)

        return allRequiredFieldsFilled && isIssuerUrlValid
      },
      redirectUrl() {
        // Construct the redirect URL
        if (!this.isEdit || !this.$route.params.id) return ""
        return `https://${this.iamDomain}/oidc/callback/${this.$route.params.id}`
      }
    },
    created() {
      if (this.isEdit) {
        this.loadProvider()
      }
    },
    mounted() {
      this.loading = false
    },
    methods: {
      loadProvider() {
        this.loading = true
        this.call(
          "getOIDCProvider",
          { providerId: this.$route.params.id },
          (data) => {
            // Make a copy of the provider data
            const providerData = { ...data }

            // If we have scopes and fixed scopes, remove the fixed scopes from the scope field
            if (providerData.scope && this.fixedScopes) {
              // Split the scopes into arrays
              const allScopes = providerData.scope.split(" ")
              const fixedScopesArray = this.fixedScopes.split(" ")

              // Filter out fixed scopes from all scopes
              const optionalScopes = allScopes.filter(
                (scope) => !fixedScopesArray.includes(scope)
              )

              // Set the provider scope to just the optional scopes
              providerData.scope = optionalScopes.join(" ")
            }

            this.provider = providerData
          },
          (err) => {
            this.showPopup("error", err.message)
            this.goBack()
          },
          () => {
            this.loading = false
          }
        )
      },
      submit() {
        if (!this.isValidProvider) {
          this.showPopup(
            "error",
            this.$t("vco.please-fill-all-required-fields")
          )
          return
        }

        this.submitting = true
        const method = this.isEdit ? "updateOIDCProvider" : "createOIDCProvider"
        const params = this.isEdit
          ? { providerId: this.$route.params.id, payload: this.provider }
          : { payload: this.provider }

        this.call(
          method,
          params,
          () => {
            this.showPopup(
              "success",
              `Provider ${this.isEdit ? "updated" : "created"} successfully`
            )

            // this.goBack()
            // // go to edit page
            if (!this.isEdit) {
              this.$router.push({
                name: "CreateOIDCProvider",
                params: { id: this.provider.id }
              })
            }
            this.loadProvider()
          },
          (err) => {
            this.showPopup("error", err.message)
          },
          () => {
            this.submitting = false
          }
        )
      },
      goBack() {
        this.$router.push({ name: "OIDCProviders" })
      },
      copyRedirectUrl() {
        this.copy(this.redirectUrl)
        setTimeout(() => {
          this.showPopup("success", "Redirect URL copied to clipboard")
        }, 10)
      }
    }
  }
</script>
